#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快手采集工具 - 整合版本
整合了所有功能模块的完整应用程序
"""

# ============================================================================
# 导入标准库
# ============================================================================
import sys
import os
import json
import time
import logging
import re
import subprocess
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from urllib.parse import urlparse

# ============================================================================
# 导入第三方库
# ============================================================================
import requests
import pandas as pd

# ============================================================================
# 导入PyQt库 - 支持PyQt5和PyQt6
# ============================================================================
try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                QWidget, QPushButton, QLabel, QComboBox, QTableWidget,
                                QTableWidgetItem, QHeaderView, QFrame, QMessageBox,
                                QSplitter, QGroupBox, QGridLayout, QTabWidget, QLineEdit,
                                QProgressBar, QFileDialog, QTextEdit, QCheckBox, QSpinBox)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QUrl
    from PyQt5.QtGui import QFont, QPalette, QColor
    try:
        from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEngineProfile
        from PyQt5.QtWebEngineCore import QWebEngineCookieStore
        WEBENGINE_AVAILABLE = True
    except ImportError:
        WEBENGINE_AVAILABLE = False
    PYQT_VERSION = 5
except ImportError:
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                                    QWidget, QPushButton, QLabel, QComboBox, QTableWidget,
                                    QTableWidgetItem, QHeaderView, QFrame, QMessageBox,
                                    QSplitter, QGroupBox, QGridLayout, QTabWidget, QLineEdit,
                                    QProgressBar, QFileDialog, QTextEdit, QCheckBox, QSpinBox)
        from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QUrl
        from PyQt6.QtGui import QFont, QPalette, QColor
        try:
            from PyQt6.QtWebEngineWidgets import QWebEngineView
            from PyQt6.QtWebEngineCore import QWebEngineProfile, QWebEngineCookieStore
            WEBENGINE_AVAILABLE = True
        except ImportError:
            WEBENGINE_AVAILABLE = False
        PYQT_VERSION = 6
    except ImportError:
        print("错误：需要安装 PyQt5 或 PyQt6")
        sys.exit(1)


# ============================================================================
# 类目解析模块
# ============================================================================
class CategoryParser:
    """类目解析器"""

    def __init__(self):
        self.category_tree = {}
        self.category_mapping = {}  # 存储key到名称的映射
        
    def parse_category_file(self, file_path):
        """解析类目文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析JSON数据
            data = json.loads(content)
            
            # 提取类目数据
            if 'data' in data and 'sytWebItemTopRank' in data['data']:
                for item in data['data']['sytWebItemTopRank']:
                    if item.get('code') == 'itemCategory' and 'list' in item:
                        self.parse_category_list(item['list'])
                        
            return True
            
        except Exception as e:
            print(f"解析类目文件失败: {e}")
            return False
            
    def parse_category_list(self, category_list):
        """解析类目列表"""
        for category in category_list:
            if category.get('hierarchy') == 0:  # 一级类目
                first_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': category.get('categoryPid', ''),
                    'children': {}
                }
                self.category_tree[category['label']] = first_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_second_level(category['children'], category['label'], category['key'])

    def parse_second_level(self, children, first_level, first_key):
        """解析二级类目"""
        for category in children:
            if category.get('hierarchy') == 1:  # 二级类目
                second_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': first_level,
                    'children': {}
                }
                self.category_tree[first_level]['children'][category['label']] = second_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_third_level(category['children'], first_level, category['label'], category['key'])

    def parse_third_level(self, children, first_level, second_level, second_key):
        """解析三级类目"""
        for category in children:
            if category.get('hierarchy') == 2:  # 三级类目
                third_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': second_key,
                    'children': []
                }
                self.category_tree[first_level]['children'][second_level]['children'][category['label']] = third_level_data
                self.category_mapping[category['key']] = category['label']

                if 'children' in category:
                    self.parse_fourth_level(category['children'], first_level, second_level, category['label'], category['key'])

    def parse_fourth_level(self, children, first_level, second_level, third_level, third_key):
        """解析四级类目"""
        for category in children:
            if category.get('hierarchy') == 3:  # 四级类目
                fourth_level_data = {
                    'name': category['label'],
                    'key': category['key'],
                    'categoryPid': third_key
                }
                self.category_tree[first_level]['children'][second_level]['children'][third_level]['children'].append(fourth_level_data)
                self.category_mapping[category['key']] = category['label']
                
    def save_category_data(self, output_path):
        """保存类目数据到文件"""
        try:
            # 确保data目录存在
            output_path.parent.mkdir(exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.category_tree, f, ensure_ascii=False, indent=2)
                
            return True
            
        except Exception as e:
            print(f"保存类目数据失败: {e}")
            return False
            
    def print_tree_structure(self):
        """打印树形结构（用于调试）"""
        for first_level, first_data in self.category_tree.items():
            print(f"{first_level}（一级类目 | categoryPid: {first_data['categoryPid']}）")

            for second_level, second_data in first_data['children'].items():
                print(f"└── {second_level}（二级类目 | categoryPid: \"{second_data['categoryPid']}\"）")

                for third_level, third_data in second_data['children'].items():
                    print(f"  └── {third_level}（三级类目 | categoryPid: \"{third_data['categoryPid']}\"）")

                    for fourth_data in third_data['children']:
                        print(f"    ├── {fourth_data['name']}（key: \"{fourth_data['key']}\"）")

    def print_tree_structure_sample(self):
        """打印树形结构示例（只显示第一个分支）"""
        if not self.category_tree:
            return

        # 只显示第一个一级类目的完整结构
        first_level_name = list(self.category_tree.keys())[0]
        first_data = self.category_tree[first_level_name]

        print(f"{first_level_name}（一级类目 | categoryPid: {first_data['categoryPid']}）")

        # 只显示第一个二级类目
        if first_data['children']:
            second_level_name = list(first_data['children'].keys())[0]
            second_data = first_data['children'][second_level_name]

            print(f"└── {second_level_name}（二级类目 | categoryPid: \"{second_data['categoryPid']}\"）")

            # 只显示第一个三级类目
            if second_data['children']:
                third_level_name = list(second_data['children'].keys())[0]
                third_data = second_data['children'][third_level_name]

                print(f"  └── {third_level_name}（三级类目 | categoryPid: \"{third_data['categoryPid']}\"）")

                # 显示前几个四级类目
                for i, fourth_data in enumerate(third_data['children'][:3]):
                    symbol = "├──" if i < 2 else "└──"
                    print(f"    {symbol} {fourth_data['name']}（key: \"{fourth_data['key']}\"）")

                if len(third_data['children']) > 3:
                    print(f"    └── ... 还有 {len(third_data['children']) - 3} 个四级类目")
                        
    def get_category_count(self):
        """获取类目统计信息"""
        first_count = len(self.category_tree)
        second_count = sum(len(first_data['children']) for first_data in self.category_tree.values())
        third_count = sum(
            len(second_data['children'])
            for first_data in self.category_tree.values()
            for second_data in first_data['children'].values()
        )
        fourth_count = sum(
            len(third_data['children'])
            for first_data in self.category_tree.values()
            for second_data in first_data['children'].values()
            for third_data in second_data['children'].values()
        )

        return {
            'first_level': first_count,
            'second_level': second_count,
            'third_level': third_count,
            'fourth_level': fourth_count
        }


def parse_categories():
    """解析类目的主函数"""
    parser = CategoryParser()
    
    # 输入文件路径
    input_file = Path("类目响应数据.md")
    
    # 输出文件路径
    output_dir = Path("data")
    output_file = output_dir / "Category data.txt"
    
    if not input_file.exists():
        print(f"错误：未找到文件 {input_file}")
        return False
        
    print("开始解析类目数据...")
    
    # 解析文件
    if not parser.parse_category_file(input_file):
        print("解析失败")
        return False
        
    # 保存数据
    if not parser.save_category_data(output_file):
        print("保存失败")
        return False
        
    # 获取统计信息
    stats = parser.get_category_count()
    
    print(f"解析完成！")
    print(f"一级类目: {stats['first_level']} 个")
    print(f"二级类目: {stats['second_level']} 个")
    print(f"三级类目: {stats['third_level']} 个")
    print(f"四级类目: {stats['fourth_level']} 个")
    print(f"数据已保存到: {output_file}")

    # 打印部分树形结构示例
    print("\n树形结构示例:")
    parser.print_tree_structure_sample()

    return True


# ============================================================================
# Cookie管理模块
# ============================================================================
class CookieManager:
    """Cookie管理器"""

    def __init__(self, cookie_store):
        self.cookie_store = cookie_store
        self.cookies = []
        self.cookie_store.cookieAdded.connect(self.on_cookie_added)
        self.cookie_store.cookieRemoved.connect(self.on_cookie_removed)

    def on_cookie_added(self, cookie):
        """Cookie添加时的回调"""
        cookie_data = {
            'name': cookie.name().data().decode('utf-8'),
            'value': cookie.value().data().decode('utf-8'),
            'domain': cookie.domain(),
            'path': cookie.path(),
            'secure': cookie.isSecure(),
            'httpOnly': cookie.isHttpOnly()
        }

        # 避免重复添加
        existing_cookie = next((c for c in self.cookies
                              if c['name'] == cookie_data['name'] and
                                 c['domain'] == cookie_data['domain']), None)
        if existing_cookie:
            self.cookies.remove(existing_cookie)

        self.cookies.append(cookie_data)

    def on_cookie_removed(self, cookie):
        """Cookie移除时的回调"""
        cookie_name = cookie.name().data().decode('utf-8')
        cookie_domain = cookie.domain()
        self.cookies = [c for c in self.cookies
                       if not (c['name'] == cookie_name and c['domain'] == cookie_domain)]

    def get_cookies_for_domain(self, url):
        """获取指定域名的Cookie"""
        parsed_url = urlparse(url)
        hostname = parsed_url.hostname
        if not hostname:
            return []

        # 获取主域名（例如：xiaohongshu.com）
        domain_parts = hostname.split('.')
        if len(domain_parts) >= 2:
            main_domain = '.'.join(domain_parts[-2:])
        else:
            main_domain = hostname

        # 过滤相关域名的Cookie
        relevant_cookies = []
        for cookie in self.cookies:
            cookie_domain = cookie['domain']
            if (main_domain in cookie_domain or
                cookie_domain == '.' + main_domain or
                cookie_domain == main_domain or
                hostname in cookie_domain or
                cookie_domain.endswith('.' + main_domain)):
                relevant_cookies.append(cookie)

        return relevant_cookies


class CookieExportWorker(QThread):
    """Cookie导出工作线程"""
    finished = pyqtSignal(str, int)  # 结果消息, Cookie数量
    error = pyqtSignal(str)  # 错误消息
    progress = pyqtSignal(str)  # 进度消息
    url_changed = pyqtSignal(str)  # URL变化信号

    def __init__(self, cookie_manager, browser_page, browser_view):
        super().__init__()
        self.cookie_manager = cookie_manager
        self.browser_page = browser_page
        self.browser_view = browser_view
        self.js_cookies = []

        # 定义要采集的网页
        self.urls = [
            {
                'url': 'https://syt.kwaixiaodian.com/zones/home',
                'filename': 'cookies.txt',
                'name': '网页1'
            },
            {
                'url': 'https://cps.kwaixiaodian.com/pc/promoter/selection-center/home',
                'filename': 'cookies2.txt',
                'name': '网页2'
            }
        ]
        self.current_index = 0

    def run(self):
        try:
            self.progress.emit("开始采集Cookie...")
            self.process_next_url()

        except Exception as e:
            self.error.emit(f"导出 Cookie 时出错：{str(e)}")

    def process_next_url(self):
        """处理下一个URL"""
        if self.current_index < len(self.urls):
            url_info = self.urls[self.current_index]
            url = url_info['url']
            name = url_info['name']

            self.progress.emit(f"正在访问{name}: {url}")

            # 发送URL变化信号，让主线程加载页面
            self.url_changed.emit(url)

            # 等待页面加载
            self.msleep(3000)  # 等待3秒让页面完全加载

            # 获取并保存当前页面的Cookie
            self.export_current_cookies()

        else:
            # 所有URL处理完成
            self.finished.emit("所有网页Cookie采集完成！", 0)

    def export_current_cookies(self):
        """导出当前页面的Cookie"""
        try:
            url_info = self.urls[self.current_index]
            url = url_info['url']
            filename = url_info['filename']
            name = url_info['name']

            self.progress.emit(f"正在获取{name}的Cookie...")

            # 等待JavaScript Cookie获取完成
            self.msleep(1000)

            # 从Cookie管理器获取Cookie
            store_cookies = self.cookie_manager.get_cookies_for_domain(url)

            # 合并JavaScript获取的Cookie和存储的Cookie
            all_cookies = []

            # 添加JavaScript获取的Cookie
            for js_cookie in self.js_cookies:
                if js_cookie.get('name') and js_cookie.get('value'):
                    all_cookies.append(js_cookie)

            # 添加存储的Cookie（避免重复）
            for store_cookie in store_cookies:
                existing = next((c for c in all_cookies
                               if c.get('name') == store_cookie['name']), None)
                if not existing:
                    all_cookies.append(store_cookie)

            if not all_cookies:
                self.progress.emit(f"⚠️ {name}未找到Cookie，继续下一个...")
            else:
                # 格式化Cookie字符串
                cookie_string = '; '.join([f"{cookie['name']}={cookie['value']}"
                                         for cookie in all_cookies
                                         if cookie.get('name') and cookie.get('value')])

                if cookie_string:
                    # 创建data目录
                    data_dir = Path("data")
                    data_dir.mkdir(exist_ok=True)

                    # 保存Cookie到指定文件
                    cookie_file = data_dir / filename
                    with open(cookie_file, 'w', encoding='utf-8') as f:
                        f.write(cookie_string)

                    self.progress.emit(f"✅ {name}成功保存 {len(all_cookies)} 个Cookie到 {filename}")
                else:
                    self.progress.emit(f"⚠️ {name}未找到有效Cookie，继续下一个...")

            # 处理下一个URL
            self.current_index += 1
            self.msleep(1000)  # 等待1秒
            self.process_next_url()

        except Exception as e:
            self.error.emit(f"处理{name}时出错：{str(e)}")

    def set_js_cookies(self, cookies):
        """设置JavaScript获取的Cookie"""
        self.js_cookies = cookies if cookies else []


class CookieExporterMainWindow(QMainWindow):
    """Cookie导出工具主窗口"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        if WEBENGINE_AVAILABLE:
            self.setup_browser()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("Cookies获取")
        self.setGeometry(100, 100, 1200, 800)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建工具栏布局
        toolbar_layout = QHBoxLayout()

        # 导出Cookie按钮
        self.export_button = QPushButton("一键导出 Cookie")
        self.export_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        self.export_button.clicked.connect(self.export_cookies)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
                font-weight: bold;
            }
        """)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        toolbar_layout.addWidget(self.status_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.export_button)

        main_layout.addLayout(toolbar_layout)
        main_layout.addWidget(self.progress_bar)

        # 创建浏览器视图（如果WebEngine可用）
        if WEBENGINE_AVAILABLE:
            self.browser = QWebEngineView()
            main_layout.addWidget(self.browser)
        else:
            # 如果WebEngine不可用，显示提示信息
            info_label = QLabel("WebEngine不可用，无法显示浏览器界面")
            info_label.setAlignment(Qt.AlignCenter)
            main_layout.addWidget(info_label)

    def setup_browser(self):
        """设置浏览器"""
        if not WEBENGINE_AVAILABLE:
            return

        # 创建自定义配置文件
        self.profile = QWebEngineProfile.defaultProfile()
        self.cookie_store = self.profile.cookieStore()

        # 创建Cookie管理器
        self.cookie_manager = CookieManager(self.cookie_store)

        # 加载默认页面
        self.browser.load(QUrl("https://syt.kwaixiaodian.com/zones/home"))

        # 连接URL变化信号
        self.browser.urlChanged.connect(self.on_url_changed)

    def on_url_changed(self, url):
        """URL变化时更新状态"""
        self.status_label.setText(f"当前页面: {url.toString()}")

    def export_cookies(self):
        """导出Cookie - 自动采集两个网页"""
        if not WEBENGINE_AVAILABLE:
            QMessageBox.warning(self, "警告", "WebEngine不可用，无法导出Cookie")
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度

        # 创建工作线程
        self.worker = CookieExportWorker(self.cookie_manager, self.browser.page(), self.browser)
        self.worker.finished.connect(self.on_export_finished)
        self.worker.error.connect(self.on_export_error)
        self.worker.progress.connect(self.on_export_progress)
        self.worker.url_changed.connect(self.on_worker_url_changed)

        # 启动工作线程
        self.worker.start()

    def get_page_cookies(self):
        """获取当前页面的Cookie"""
        if not WEBENGINE_AVAILABLE:
            return

        # 通过JavaScript获取Cookie
        script = """
        (function() {
            if (!document.cookie) return [];
            return document.cookie.split(';').map(cookie => {
                const parts = cookie.trim().split('=');
                const name = parts[0];
                const value = parts.slice(1).join('=');
                return {
                    name: name,
                    value: value || '',
                    domain: window.location.hostname
                };
            }).filter(cookie => cookie.name);
        })();
        """

        def handle_cookies(result):
            if result and hasattr(self, 'worker') and self.worker:
                self.worker.set_js_cookies(result)

        self.browser.page().runJavaScript(script, handle_cookies)

    def on_worker_url_changed(self, url):
        """工作线程请求加载新URL"""
        if WEBENGINE_AVAILABLE:
            self.browser.load(QUrl(url))
            # 等待页面加载完成后获取Cookie
            self.browser.loadFinished.connect(self.on_page_load_finished)

    def on_page_load_finished(self, success):
        """页面加载完成后获取Cookie"""
        if success and hasattr(self, 'worker') and self.worker:
            # 获取当前页面的Cookie
            self.get_page_cookies()
            # 断开信号避免重复连接
            try:
                self.browser.loadFinished.disconnect(self.on_page_load_finished)
            except:
                pass

    def on_export_progress(self, message):
        """更新导出进度"""
        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                background-color: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
                font-weight: bold;
            }
        """)

    def on_export_finished(self, message, cookie_count):
        """导出完成"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(message)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
                font-weight: bold;
            }
        """)

        # 显示完成消息
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Information)
        msg_box.setWindowTitle("采集完成")
        msg_box.setText("Cookie采集完成！\n\n"
                       "网页1 (快手小店后台): data/cookies.txt\n"
                       "网页2 (快手小店CPS): data/cookies2.txt\n\n"
                       "请查看data目录下的文件。\n\n"
                       "浏览器窗口将在0.5秒后自动关闭。")
        msg_box.setStandardButtons(QMessageBox.Ok)

        # 显示消息框
        msg_box.exec_() if PYQT_VERSION == 5 else msg_box.exec()

        # 延迟0.05秒后自动关闭窗口
        if PYQT_VERSION == 5:
            from PyQt5.QtCore import QTimer
        else:
            from PyQt6.QtCore import QTimer
        QTimer.singleShot(5, self.close_application)

    def on_export_error(self, error_message):
        """导出错误"""
        self.progress_bar.setVisible(False)
        self.status_label.setText(error_message)
        self.status_label.setStyleSheet("""
            QLabel {
                padding: 8px;
                border-radius: 4px;
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                font-weight: bold;
            }
        """)

        QMessageBox.critical(self, "错误", error_message)

    def close_application(self):
        """关闭应用程序"""
        self.close()
        # 确保应用程序完全退出
        import sys
        sys.exit(0)


# ============================================================================
# 数据采集模块
# ============================================================================
class DataCollector(QThread):
    """数据采集器 - 优化版本"""

    # 信号定义
    progress_updated = pyqtSignal(str)  # 进度更新信号
    status_changed = pyqtSignal(str)    # 状态变化信号
    error_occurred = pyqtSignal(str)    # 错误信号
    data_row_ready = pyqtSignal(dict)   # 单行数据就绪信号
    collection_finished = pyqtSignal()  # 采集完成信号

    def __init__(self, filters: Dict[str, Any]):
        super().__init__()
        self.filters = filters
        self.is_running = False
        self.should_stop = False
        self.category_data = {}

        # 设置日志
        self.setup_logging()

        # 加载Cookie
        self.cookies = self.load_cookies()

        # 请求头
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://syt.kwaixiaodian.com/',
            'Origin': 'https://syt.kwaixiaodian.com'
        }

    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger('DataCollector')
        self.logger.setLevel(logging.INFO)

        # 避免重复添加处理器
        if not self.logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler('data_collector.log', encoding='utf-8')
            file_handler.setLevel(logging.INFO)

            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)

            # 格式化器
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)

    def load_cookies(self) -> str:
        """加载Cookie"""
        try:
            cookie_file = Path("data/cookies.txt")
            if cookie_file.exists():
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            return ""
        except Exception as e:
            self.logger.error(f"加载Cookie失败: {e}")
            return ""

    def load_category_data(self) -> bool:
        """加载类目数据"""
        try:
            category_file = Path("data/Category data.txt")
            if not category_file.exists():
                self.logger.error("类目数据文件不存在")
                return False

            with open(category_file, 'r', encoding='utf-8') as f:
                self.category_data = json.load(f)

            self.logger.info(f"成功加载类目数据，共 {len(self.category_data)} 个一级类目")
            return True

        except Exception as e:
            self.logger.error(f"加载类目数据失败: {e}")
            return False

    def stop(self):
        """停止采集"""
        self.should_stop = True
        self.logger.info("收到停止信号")

    def validate_filters(self) -> bool:
        """验证筛选条件"""
        try:
            # 检查必要的筛选条件
            if not self.filters.get('date_range'):
                self.error_occurred.emit("请选择日期范围")
                return False

            if not self.cookies:
                self.error_occurred.emit("未找到Cookie，请先登录")
                return False

            return True

        except Exception as e:
            self.logger.error(f"验证筛选条件失败: {e}")
            self.error_occurred.emit(f"验证筛选条件失败: {e}")
            return False

    def parse_date_range(self) -> Optional[Dict[str, str]]:
        """解析日期范围"""
        try:
            date_range = self.filters.get('date_range', '')

            if date_range == "昨天":
                yesterday = datetime.now() - timedelta(days=1)
                current_start = current_end = yesterday.strftime('%Y-%m-%d')
                compare_start = compare_end = (yesterday - timedelta(days=7)).strftime('%Y-%m-%d')
            elif date_range == "最近7天":
                end_date = datetime.now() - timedelta(days=1)
                start_date = end_date - timedelta(days=6)
                current_start = start_date.strftime('%Y-%m-%d')
                current_end = end_date.strftime('%Y-%m-%d')
                compare_start = (start_date - timedelta(days=7)).strftime('%Y-%m-%d')
                compare_end = (end_date - timedelta(days=7)).strftime('%Y-%m-%d')
            elif date_range == "最近30天":
                end_date = datetime.now() - timedelta(days=1)
                start_date = end_date - timedelta(days=29)
                current_start = start_date.strftime('%Y-%m-%d')
                current_end = end_date.strftime('%Y-%m-%d')
                compare_start = (start_date - timedelta(days=30)).strftime('%Y-%m-%d')
                compare_end = (end_date - timedelta(days=30)).strftime('%Y-%m-%d')
            else:
                self.error_occurred.emit(f"不支持的日期范围: {date_range}")
                return None

            return {
                'current_start': current_start,
                'current_end': current_end,
                'compare_start': compare_start,
                'compare_end': compare_end
            }

        except Exception as e:
            self.logger.error(f"解析日期范围失败: {e}")
            self.error_occurred.emit(f"解析日期范围失败: {e}")
            return None

    def generate_request_payload(self) -> Optional[Dict[str, Any]]:
        """生成请求载荷"""
        try:
            # 阶段一：请求准备
            self.progress_updated.emit("正在准备请求载荷...")

            # 验证筛选条件
            if not self.validate_filters():
                return None

            # 解析日期
            date_range = self.parse_date_range()
            if not date_range:
                return None

            # 构建基础载荷
            payload = {
                "module": "sytWebItemTopRank",
                "timeRange": "CUSTOMIZED_WEEK",
                "currentStartDay": date_range["current_start"],
                "currentEndDay": date_range["current_end"],
                "compareStartDay": date_range["compare_start"],
                "compareEndDay": date_range["compare_end"],
                "param": [],
                "pageNum": 1,
                "pageSize": 100
            }

            # 添加筛选参数
            self.add_filter_params(payload)

            self.logger.info(f"生成请求载荷: {json.dumps(payload, ensure_ascii=False)}")
            return payload

        except Exception as e:
            self.logger.error(f"生成请求载荷失败: {e}")
            self.error_occurred.emit(f"生成请求载荷失败: {e}")
            return None

    def add_filter_params(self, payload: Dict[str, Any]):
        """添加筛选参数"""
        try:
            # 添加类目筛选
            category_filter = self.filters.get('category')
            if category_filter and category_filter != "全部":
                # 查找类目key
                category_key = self.find_category_key(category_filter)
                if category_key:
                    payload["param"].append({
                        "code": "itemCategory",
                        "value": [category_key]
                    })

            # 添加渠道筛选
            channel_filter = self.filters.get('channel')
            if channel_filter and channel_filter != "全部":
                channel_mapping = {
                    "快手小店": "KWAI_SHOP",
                    "第三方": "THIRD_PARTY"
                }
                if channel_filter in channel_mapping:
                    payload["param"].append({
                        "code": "channel",
                        "value": [channel_mapping[channel_filter]]
                    })

            # 添加其他筛选条件
            trade_index_filter = self.filters.get('trade_index')
            if trade_index_filter and trade_index_filter != "全部":
                payload["param"].append({
                    "code": "tradeIndex",
                    "value": [trade_index_filter]
                })

        except Exception as e:
            self.logger.error(f"添加筛选参数失败: {e}")

    def find_category_key(self, category_name: str) -> Optional[str]:
        """查找类目key"""
        try:
            for first_level, first_data in self.category_data.items():
                if first_level == category_name:
                    return first_data.get('key')

                for second_level, second_data in first_data.get('children', {}).items():
                    if second_level == category_name:
                        return second_data.get('key')

                    for third_level, third_data in second_data.get('children', {}).items():
                        if third_level == category_name:
                            return third_data.get('key')

                        for fourth_data in third_data.get('children', []):
                            if fourth_data.get('name') == category_name:
                                return fourth_data.get('key')

            return None

        except Exception as e:
            self.logger.error(f"查找类目key失败: {e}")
            return None

    def execute_request(self, payload: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行请求"""
        try:
            url = "https://syt.kwaixiaodian.com/rest/app/gateway/rank/list"

            self.progress_updated.emit("正在发送请求...")

            response = requests.post(
                url,
                json=payload,
                headers=self.headers,
                cookies={'cookie': self.cookies},
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('result') == 200:
                    self.logger.info("请求成功")
                    return data
                else:
                    error_msg = data.get('error_msg', '未知错误')
                    self.logger.error(f"API返回错误: {error_msg}")
                    self.error_occurred.emit(f"API返回错误: {error_msg}")
                    return None
            else:
                self.logger.error(f"HTTP请求失败: {response.status_code}")
                self.error_occurred.emit(f"HTTP请求失败: {response.status_code}")
                return None

        except requests.exceptions.Timeout:
            self.logger.error("请求超时")
            self.error_occurred.emit("请求超时，请检查网络连接")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"请求异常: {e}")
            self.error_occurred.emit(f"请求异常: {e}")
            return None
        except Exception as e:
            self.logger.error(f"执行请求失败: {e}")
            self.error_occurred.emit(f"执行请求失败: {e}")
            return None

    def run(self):
        """主运行方法 - 优化的两阶段采集流程"""
        try:
            self.is_running = True
            self.should_stop = False

            self.progress_updated.emit("开始数据采集...")
            self.status_changed.emit("采集中")

            # 加载必要数据
            if not self.load_category_data():
                self.error_occurred.emit("加载类目数据失败")
                return

            # 第一阶段：基础数据批量采集
            self.progress_updated.emit("第一阶段：获取基础数据...")
            basic_data = self.collect_basic_data()
            if not basic_data:
                self.error_occurred.emit("基础数据采集失败")
                return

            # 第二阶段：扩展数据逐条采集并逐行显示
            self.progress_updated.emit("第二阶段：获取扩展数据...")
            self.collect_extended_data_and_display(basic_data)

        except Exception as e:
            self.logger.error(f"采集过程出错: {e}")
            self.error_occurred.emit(f"采集失败: {e}")
        finally:
            self.is_running = False
            self.status_changed.emit("就绪")
            self.collection_finished.emit()

    def collect_basic_data(self) -> Optional[List[Dict[str, Any]]]:
        """第一阶段：基础数据批量采集"""
        try:
            # 生成请求载荷
            payload = self.generate_request_payload()
            if not payload:
                return None

            # 执行请求
            response_data = self.execute_request(payload)
            if not response_data:
                return None

            # 提取基础数据列表
            data_list = response_data.get('data', {}).get('data', [])
            if not data_list:
                self.logger.warning("响应中没有数据")
                self.error_occurred.emit("未获取到数据")
                return None

            self.logger.info(f"成功获取 {len(data_list)} 条基础数据")
            return data_list

        except Exception as e:
            self.logger.error(f"基础数据采集失败: {e}")
            self.error_occurred.emit(f"基础数据采集失败: {e}")
            return None

    def collect_extended_data_and_display(self, basic_data: List[Dict[str, Any]]):
        """第二阶段：扩展数据采集并逐行显示"""
        try:
            total_count = len(basic_data)
            self.progress_updated.emit(f"开始处理 {total_count} 条数据...")

            # 使用线程池进行并发处理
            with ThreadPoolExecutor(max_workers=3) as executor:
                # 提交所有任务
                future_to_index = {}
                for index, item in enumerate(basic_data):
                    if self.should_stop:
                        break
                    future = executor.submit(self.process_single_item, item, index + 1, total_count)
                    future_to_index[future] = index

                # 处理完成的任务
                for future in as_completed(future_to_index):
                    if self.should_stop:
                        break

                    try:
                        result = future.result()
                        if result:
                            # 发送单行数据信号
                            self.data_row_ready.emit(result)
                    except Exception as e:
                        self.logger.error(f"处理任务失败: {e}")

            if not self.should_stop:
                self.progress_updated.emit("数据采集完成")
                self.logger.info("数据采集完成")

        except Exception as e:
            self.logger.error(f"扩展数据采集失败: {e}")
            self.error_occurred.emit(f"扩展数据采集失败: {e}")

    def process_single_item(self, item: Dict[str, Any], index: int, total: int) -> Optional[Dict[str, Any]]:
        """处理单个商品数据"""
        try:
            if self.should_stop:
                return None

            # 更新进度
            self.progress_updated.emit(f"正在处理第 {index}/{total} 条数据...")

            # 提取基础信息
            item_id = item.get('itemId', '')
            item_title = item.get('itemTitle', '')

            # 获取扩展数据
            extended_data = self.get_extended_data(item_id)

            # 合并数据
            result = {
                'itemId': item_id,
                'itemTitle': item_title,
                'currentGmv': item.get('currentGmv', 0),
                'currentOrderCnt': item.get('currentOrderCnt', 0),
                'currentUv': item.get('currentUv', 0),
                'currentCvr': item.get('currentCvr', 0),
                'currentPrice': item.get('currentPrice', 0),
                'tradeIndex': item.get('tradeIndex', 0),
                'channelRatio': item.get('channelRatio', 0),
                'rank': item.get('rank', 0),
                'categoryName': item.get('categoryName', ''),
                'shopName': item.get('shopName', ''),
                'brandName': item.get('brandName', ''),
            }

            # 添加扩展数据
            if extended_data:
                result.update(extended_data)

            # 应用筛选条件
            if self.apply_filters(result):
                return result
            else:
                return None

        except Exception as e:
            self.logger.error(f"处理单个商品数据失败: {e}")
            return None

    def get_extended_data(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取商品扩展数据"""
        try:
            # 这里可以添加获取商品详细信息的逻辑
            # 暂时返回空字典
            return {}

        except Exception as e:
            self.logger.error(f"获取扩展数据失败: {e}")
            return None

    def apply_filters(self, data: Dict[str, Any]) -> bool:
        """应用筛选条件"""
        try:
            # 交易指数筛选
            trade_index_filter = self.filters.get('trade_index_min')
            if trade_index_filter and data.get('tradeIndex', 0) < trade_index_filter:
                return False

            # 渠道占比筛选
            channel_ratio_filter = self.filters.get('channel_ratio_min')
            if channel_ratio_filter and data.get('channelRatio', 0) < channel_ratio_filter:
                return False

            return True

        except Exception as e:
            self.logger.error(f"应用筛选条件失败: {e}")
            return True


# ============================================================================
# 商品查询模块
# ============================================================================
class ProductQueryWorker(QThread):
    """商品销量查询工作线程"""

    # 信号定义
    progress_updated = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    query_finished = pyqtSignal()
    result_ready = pyqtSignal(dict)

    def __init__(self, product_links: List[str], date_range: str, speed_mode: str = "normal"):
        super().__init__()
        self.product_links = product_links
        self.date_range = date_range
        self.speed_mode = speed_mode
        self.is_running = False
        self.should_stop = False

        # 设置日志
        self.setup_logging()

        # 加载Cookie
        self.cookies = self.load_cookies()

        # 请求头
        self.headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://syt.kwaixiaodian.com/',
            'Origin': 'https://syt.kwaixiaodian.com'
        }

    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger('ProductQueryWorker')
        self.logger.setLevel(logging.INFO)

        # 避免重复添加处理器
        if not self.logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler('product_query.log', encoding='utf-8')
            file_handler.setLevel(logging.INFO)

            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)

            # 格式化器
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)

            self.logger.addHandler(file_handler)
            self.logger.addHandler(console_handler)

    def load_cookies(self) -> str:
        """加载Cookie"""
        try:
            cookie_file = Path("data/cookies.txt")
            if cookie_file.exists():
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    return f.read().strip()
            return ""
        except Exception as e:
            self.logger.error(f"加载Cookie失败: {e}")
            return ""

    def stop(self):
        """停止查询"""
        self.should_stop = True
        self.logger.info("收到停止信号")

    def extract_product_id(self, url: str) -> Optional[str]:
        """从URL中提取商品ID"""
        try:
            # 使用正则表达式提取商品ID
            pattern = r'itemId[=/](\d+)'
            match = re.search(pattern, url)
            if match:
                return match.group(1)

            # 尝试其他可能的模式
            pattern = r'id[=/](\d+)'
            match = re.search(pattern, url)
            if match:
                return match.group(1)

            return None

        except Exception as e:
            self.logger.error(f"提取商品ID失败: {e}")
            return None

    def query_product_title(self, product_id: str, date: str) -> Optional[str]:
        """查询商品标题"""
        try:
            url = "https://syt.kwaixiaodian.com/rest/app/gateway/commodity/info"

            payload = {
                "itemId": product_id,
                "timeRange": "CUSTOMIZED_DAY",
                "currentStartDay": date,
                "currentEndDay": date
            }

            response = requests.post(
                url,
                json=payload,
                headers=self.headers,
                cookies={'cookie': self.cookies},
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('result') == 200 and 'data' in data:
                    return data['data'].get('itemTitle', '')

            return None

        except Exception as e:
            self.logger.error(f"查询商品标题失败: {e}")
            return None

    def query_sales_data(self, product_id: str, date: str) -> int:
        """查询指定日期的成交量数据"""
        try:
            url = "https://syt.kwaixiaodian.com/rest/app/gateway/rank/list"

            payload = {
                "module": "sytWebItemTopRank4Seller",
                "pageNum": 1,
                "pageSize": 10,
                "timeRange": "CUSTOMIZED_DAY",
                "currentStartDay": date,
                "currentEndDay": date,
                "param": [{"code": "itemId", "value": [product_id]}]
            }

            response = requests.post(
                url,
                json=payload,
                headers=self.headers,
                cookies={'cookie': self.cookies},
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('result') == 200 and 'data' in data:
                    data_list = data['data'].get('data', [])
                    if data_list:
                        return data_list[0].get('currentOrderCnt', 0)

            return 0

        except Exception as e:
            self.logger.error(f"查询销量数据失败: {e}")
            return 0

    def generate_date_list(self) -> List[str]:
        """生成日期列表"""
        try:
            if self.date_range == "昨天":
                yesterday = datetime.now() - timedelta(days=1)
                return [yesterday.strftime('%Y-%m-%d')]
            elif self.date_range == "最近7天":
                dates = []
                for i in range(7):
                    date = datetime.now() - timedelta(days=i+1)
                    dates.append(date.strftime('%Y-%m-%d'))
                return dates
            elif self.date_range == "最近30天":
                dates = []
                for i in range(30):
                    date = datetime.now() - timedelta(days=i+1)
                    dates.append(date.strftime('%Y-%m-%d'))
                return dates
            else:
                return []

        except Exception as e:
            self.logger.error(f"生成日期列表失败: {e}")
            return []

    def run(self):
        """执行查询（优化版本）"""
        try:
            self.is_running = True
            self.should_stop = False

            if not self.cookies:
                self.error_occurred.emit("未找到cookies，请先登录")
                return

            if not self.product_links:
                self.error_occurred.emit("没有要查询的商品链接")
                return

            # 根据速度模式选择执行方式
            if self.speed_mode == "fast" and len(self.product_links) > 3:
                # 快速模式且商品数量较多时使用批量处理
                self.run_batch_mode()
            else:
                # 普通模式使用逐个处理
                self.run_normal_mode()

            self.progress_updated.emit("查询完成")
            self.query_finished.emit()

        except Exception as e:
            self.logger.error(f"查询过程出错: {e}")
            self.error_occurred.emit(f"查询过程出错: {str(e)}")
        finally:
            self.is_running = False

    def run_normal_mode(self):
        """普通模式执行查询"""
        try:
            # 生成日期列表
            date_list = self.generate_date_list()
            total_products = len(self.product_links)

            for product_index, product_link in enumerate(self.product_links):
                if self.should_stop:
                    break

                self.progress_updated.emit(f"正在处理商品 {product_index + 1}/{total_products}")

                # 提取商品ID
                product_id = self.extract_product_id(product_link)
                if not product_id:
                    self.logger.warning(f"无法提取商品ID: {product_link}")
                    continue

                # 查询商品标题
                product_title = self.query_product_title(product_id, date_list[0] if date_list else datetime.now().strftime('%Y-%m-%d'))

                # 查询每日销量
                daily_sales = {}
                total_sales = 0

                for date in date_list:
                    if self.should_stop:
                        break

                    sales = self.query_sales_data(product_id, date)
                    daily_sales[date] = sales
                    total_sales += sales

                    # 根据速度模式添加延迟
                    delay = self.get_delay_by_speed_mode()
                    if delay > 0:
                        time.sleep(delay)

                # 发送结果
                result = {
                    'product_id': product_id,
                    'product_title': product_title or '未知商品',
                    'product_link': product_link,
                    'daily_sales': daily_sales,
                    'total_sales': total_sales,
                    'date_range': self.date_range
                }

                self.result_ready.emit(result)

        except Exception as e:
            self.logger.error(f"普通模式查询失败: {e}")
            self.error_occurred.emit(f"查询失败: {str(e)}")

    def run_batch_mode(self):
        """批量模式执行查询"""
        try:
            # 生成日期列表
            date_list = self.generate_date_list()

            # 使用线程池进行并发查询
            with ThreadPoolExecutor(max_workers=3) as executor:
                future_to_product = {}

                for product_link in self.product_links:
                    if self.should_stop:
                        break
                    future = executor.submit(self.query_single_product, product_link, date_list)
                    future_to_product[future] = product_link

                # 处理完成的任务
                for future in as_completed(future_to_product):
                    if self.should_stop:
                        break

                    try:
                        result = future.result()
                        if result:
                            self.result_ready.emit(result)
                    except Exception as e:
                        product_link = future_to_product[future]
                        self.logger.error(f"查询商品失败 {product_link}: {e}")

        except Exception as e:
            self.logger.error(f"批量模式查询失败: {e}")
            self.error_occurred.emit(f"批量查询失败: {str(e)}")

    def query_single_product(self, product_link: str, date_list: List[str]) -> Optional[Dict[str, Any]]:
        """查询单个商品"""
        try:
            # 提取商品ID
            product_id = self.extract_product_id(product_link)
            if not product_id:
                return None

            # 查询商品标题
            product_title = self.query_product_title(product_id, date_list[0] if date_list else datetime.now().strftime('%Y-%m-%d'))

            # 查询每日销量
            daily_sales = {}
            total_sales = 0

            for date in date_list:
                if self.should_stop:
                    break

                sales = self.query_sales_data(product_id, date)
                daily_sales[date] = sales
                total_sales += sales

                # 添加延迟
                delay = self.get_delay_by_speed_mode()
                if delay > 0:
                    time.sleep(delay)

            return {
                'product_id': product_id,
                'product_title': product_title or '未知商品',
                'product_link': product_link,
                'daily_sales': daily_sales,
                'total_sales': total_sales,
                'date_range': self.date_range
            }

        except Exception as e:
            self.logger.error(f"查询单个商品失败: {e}")
            return None

    def get_delay_by_speed_mode(self) -> float:
        """根据速度模式获取延迟时间"""
        speed_delays = {
            "ultra_fast": 0.1,
            "fast": 0.5,
            "normal": 1.0,
            "slow": 2.0
        }
        return speed_delays.get(self.speed_mode, 1.0)


# ============================================================================
# 自定义组件
# ============================================================================
class NoAnimationComboBox(QComboBox):
    """无动画下拉框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        # 禁用动画效果
        self.setStyleSheet("""
            QComboBox {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }
            QComboBox:hover {
                border-color: #007acc;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #ccc;
                background-color: white;
                selection-background-color: #e3f2fd;
            }
        """)

    def showPopup(self):
        """重写showPopup方法，立即显示所有选项"""
        super().showPopup()
        # 确保下拉列表立即显示所有选项
        if self.view():
            self.view().setMinimumWidth(self.width())


# ============================================================================
# 主窗口类
# ============================================================================
class KuaishouCollectorMainWindow(QMainWindow):
    """快手采集工具主窗口"""

    def __init__(self):
        super().__init__()
        self.data_collector = None
        self.product_query_worker = None
        self.category_data = {}
        self.collected_data = []
        self.query_results = []

        # 初始化界面
        self.init_ui()

        # 加载数据
        self.load_category_data()

        # 检查Cookie状态
        self.check_cookies()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("快手采集工具")
        self.setGeometry(100, 100, 1400, 900)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 创建数据采集标签页
        self.create_collection_tab()

        # 创建商品查询标签页
        self.create_query_tab()

        # 创建状态栏
        self.create_status_bar()

    def create_collection_tab(self):
        """创建数据采集标签页"""
        collection_widget = QWidget()
        self.tab_widget.addTab(collection_widget, "数据采集")

        layout = QVBoxLayout(collection_widget)

        # 创建筛选区域
        filter_group = QGroupBox("筛选条件")
        filter_layout = QGridLayout(filter_group)

        # 日期范围
        filter_layout.addWidget(QLabel("日期范围:"), 0, 0)
        self.date_range_combo = NoAnimationComboBox()
        self.date_range_combo.addItems(["昨天", "最近7天", "最近30天"])
        self.date_range_combo.setCurrentText("昨天")
        filter_layout.addWidget(self.date_range_combo, 0, 1)

        # 类目筛选
        filter_layout.addWidget(QLabel("类目:"), 0, 2)
        self.category_combo = NoAnimationComboBox()
        self.category_combo.addItem("全部")
        filter_layout.addWidget(self.category_combo, 0, 3)

        # 渠道筛选
        filter_layout.addWidget(QLabel("渠道:"), 1, 0)
        self.channel_combo = NoAnimationComboBox()
        self.channel_combo.addItems(["全部", "快手小店", "第三方"])
        filter_layout.addWidget(self.channel_combo, 1, 1)

        # 交易指数筛选
        filter_layout.addWidget(QLabel("最小交易指数:"), 1, 2)
        self.trade_index_input = QLineEdit()
        self.trade_index_input.setPlaceholderText("输入最小交易指数")
        filter_layout.addWidget(self.trade_index_input, 1, 3)

        layout.addWidget(filter_group)

        # 创建按钮区域
        button_layout = QHBoxLayout()

        # 解析类目按钮
        self.parse_category_btn = QPushButton("解析类目")
        self.parse_category_btn.clicked.connect(self.on_parse_category_clicked)
        button_layout.addWidget(self.parse_category_btn)

        # 登录按钮
        self.login_btn = QPushButton("登录")
        self.login_btn.clicked.connect(self.on_login_clicked)
        button_layout.addWidget(self.login_btn)

        # 开始采集按钮
        self.start_collection_btn = QPushButton("开始采集")
        self.start_collection_btn.clicked.connect(self.on_start_collection_clicked)
        self.start_collection_btn.setEnabled(False)
        button_layout.addWidget(self.start_collection_btn)

        # 停止采集按钮
        self.stop_collection_btn = QPushButton("停止采集")
        self.stop_collection_btn.clicked.connect(self.on_stop_collection_clicked)
        self.stop_collection_btn.setEnabled(False)
        button_layout.addWidget(self.stop_collection_btn)

        # 导出数据按钮
        self.export_data_btn = QPushButton("导出数据")
        self.export_data_btn.clicked.connect(self.on_export_data_clicked)
        self.export_data_btn.setEnabled(False)
        button_layout.addWidget(self.export_data_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 创建数据表格
        self.collection_table = QTableWidget()
        self.setup_collection_table()
        layout.addWidget(self.collection_table)

    def create_query_tab(self):
        """创建商品查询标签页"""
        query_widget = QWidget()
        self.tab_widget.addTab(query_widget, "商品查询")

        layout = QVBoxLayout(query_widget)

        # 创建输入区域
        input_group = QGroupBox("查询设置")
        input_layout = QGridLayout(input_group)

        # 商品链接输入
        input_layout.addWidget(QLabel("商品链接:"), 0, 0)
        self.product_links_input = QLineEdit()
        self.product_links_input.setPlaceholderText("输入商品链接，多个链接用换行分隔")
        input_layout.addWidget(self.product_links_input, 0, 1, 1, 2)

        # 日期范围
        input_layout.addWidget(QLabel("查询日期:"), 1, 0)
        self.query_date_combo = NoAnimationComboBox()
        self.query_date_combo.addItems(["昨天", "最近7天", "最近30天"])
        self.query_date_combo.setCurrentText("昨天")
        input_layout.addWidget(self.query_date_combo, 1, 1)

        # 查询速度
        input_layout.addWidget(QLabel("查询速度:"), 1, 2)
        self.speed_combo = NoAnimationComboBox()
        self.speed_combo.addItems(["ultra_fast", "fast", "normal", "slow"])
        self.speed_combo.setCurrentText("normal")
        input_layout.addWidget(self.speed_combo, 1, 3)

        layout.addWidget(input_group)

        # 创建查询按钮区域
        query_button_layout = QHBoxLayout()

        # 开始查询按钮
        self.start_query_btn = QPushButton("开始查询")
        self.start_query_btn.clicked.connect(self.on_start_query_clicked)
        self.start_query_btn.setEnabled(False)
        query_button_layout.addWidget(self.start_query_btn)

        # 停止查询按钮
        self.stop_query_btn = QPushButton("停止查询")
        self.stop_query_btn.clicked.connect(self.on_stop_query_clicked)
        self.stop_query_btn.setEnabled(False)
        query_button_layout.addWidget(self.stop_query_btn)

        # 导出查询结果按钮
        self.export_query_btn = QPushButton("导出查询结果")
        self.export_query_btn.clicked.connect(self.on_export_query_clicked)
        self.export_query_btn.setEnabled(False)
        query_button_layout.addWidget(self.export_query_btn)

        query_button_layout.addStretch()
        layout.addLayout(query_button_layout)

        # 创建查询结果表格
        self.query_table = QTableWidget()
        self.setup_query_table()
        layout.addWidget(self.query_table)

    def setup_collection_table(self):
        """设置采集数据表格"""
        headers = [
            "商品ID", "商品标题", "当前GMV", "当前订单数", "当前UV",
            "当前转化率", "当前价格", "交易指数", "渠道占比", "排名",
            "类目名称", "店铺名称", "品牌名称"
        ]

        self.collection_table.setColumnCount(len(headers))
        self.collection_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.collection_table.setAlternatingRowColors(True)
        self.collection_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.collection_table.horizontalHeader().setStretchLastSection(True)

        # 调整列宽
        header = self.collection_table.horizontalHeader()
        for i in range(len(headers)):
            if i == 1:  # 商品标题列
                header.setSectionResizeMode(i, QHeaderView.Stretch)
            else:
                header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def setup_query_table(self):
        """设置查询结果表格"""
        headers = ["商品ID", "商品标题", "商品链接", "总销量", "日期范围"]

        self.query_table.setColumnCount(len(headers))
        self.query_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.query_table.setAlternatingRowColors(True)
        self.query_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.query_table.horizontalHeader().setStretchLastSection(True)

        # 调整列宽
        header = self.query_table.horizontalHeader()
        for i in range(len(headers)):
            if i in [1, 2]:  # 商品标题和链接列
                header.setSectionResizeMode(i, QHeaderView.Stretch)
            else:
                header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

    def create_status_bar(self):
        """创建状态栏"""
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                margin: 2px;
            }
        """)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # 添加到状态栏
        status_bar = self.statusBar()
        status_bar.addWidget(self.status_label, 1)
        status_bar.addPermanentWidget(self.progress_bar)

    def load_category_data(self):
        """加载类目数据"""
        try:
            category_file = Path("data/Category data.txt")
            if category_file.exists():
                with open(category_file, 'r', encoding='utf-8') as f:
                    self.category_data = json.load(f)

                # 更新类目下拉框
                self.update_category_combo()

        except Exception as e:
            print(f"加载类目数据失败: {e}")

    def update_category_combo(self):
        """更新类目下拉框"""
        try:
            self.category_combo.clear()
            self.category_combo.addItem("全部")

            # 添加一级类目
            for first_level in self.category_data.keys():
                self.category_combo.addItem(first_level)

                # 添加二级类目
                first_data = self.category_data[first_level]
                for second_level in first_data.get('children', {}).keys():
                    self.category_combo.addItem(f"  {second_level}")

                    # 添加三级类目
                    second_data = first_data['children'][second_level]
                    for third_level in second_data.get('children', {}).keys():
                        self.category_combo.addItem(f"    {third_level}")

        except Exception as e:
            print(f"更新类目下拉框失败: {e}")

    def check_cookies(self):
        """检查Cookie状态"""
        try:
            cookie_file = Path("data/cookies.txt")
            if cookie_file.exists() and cookie_file.stat().st_size > 0:
                self.status_label.setText("Cookie已加载，可以开始采集")
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                        border-radius: 4px;
                        padding: 5px;
                        margin: 2px;
                    }
                """)
                self.start_collection_btn.setEnabled(True)
                self.start_query_btn.setEnabled(True)
            else:
                self.status_label.setText("未找到Cookie，请先登录")
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #f8d7da;
                        color: #721c24;
                        border: 1px solid #f5c6cb;
                        border-radius: 4px;
                        padding: 5px;
                        margin: 2px;
                    }
                """)
                self.start_collection_btn.setEnabled(False)
                self.start_query_btn.setEnabled(False)

        except Exception as e:
            print(f"检查Cookie状态失败: {e}")

    # ========================================================================
    # 事件处理方法
    # ========================================================================
    def on_parse_category_clicked(self):
        """解析类目按钮点击事件"""
        try:
            # 调用类目解析函数
            parse_categories()

            # 重新加载类目数据
            self.load_category_data()

            # 更新状态
            self.status_label.setText("类目解析完成")

        except Exception as e:
            self.status_label.setText(f"类目解析失败: {e}")

    def on_login_clicked(self):
        """登录按钮点击事件"""
        try:
            # 启动Cookie导出工具
            if WEBENGINE_AVAILABLE:
                self.cookie_window = CookieExporterMainWindow()
                self.cookie_window.show()
            else:
                QMessageBox.warning(self, "警告", "WebEngine不可用，无法启动登录界面")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动登录界面失败: {e}")

    def on_start_collection_clicked(self):
        """开始采集按钮点击事件"""
        try:
            # 获取筛选条件
            filters = {
                'date_range': self.date_range_combo.currentText(),
                'category': self.category_combo.currentText().strip(),
                'channel': self.channel_combo.currentText(),
                'trade_index_min': float(self.trade_index_input.text()) if self.trade_index_input.text() else 0,
            }

            # 创建数据采集器
            self.data_collector = DataCollector(filters)

            # 连接信号
            self.data_collector.progress_updated.connect(self.on_collection_progress)
            self.data_collector.status_changed.connect(self.on_collection_status)
            self.data_collector.error_occurred.connect(self.on_collection_error)
            self.data_collector.data_row_ready.connect(self.on_data_row_ready)
            self.data_collector.collection_finished.connect(self.on_collection_finished)

            # 启动采集
            self.data_collector.start()

            # 更新界面状态
            self.start_collection_btn.setEnabled(False)
            self.stop_collection_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            # 清空表格
            self.collection_table.setRowCount(0)
            self.collected_data.clear()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动采集失败: {e}")

    def on_stop_collection_clicked(self):
        """停止采集按钮点击事件"""
        try:
            if self.data_collector and self.data_collector.isRunning():
                self.data_collector.stop()
                self.status_label.setText("正在停止采集...")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止采集失败: {e}")

    def on_export_data_clicked(self):
        """导出数据按钮点击事件"""
        try:
            if not self.collected_data:
                QMessageBox.information(self, "提示", "没有数据可导出")
                return

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存数据", "采集数据.csv", "CSV文件 (*.csv)"
            )

            if file_path:
                self.export_to_csv(self.collected_data, file_path)
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出数据失败: {e}")

    def on_start_query_clicked(self):
        """开始查询按钮点击事件"""
        try:
            # 获取商品链接
            links_text = self.product_links_input.text().strip()
            if not links_text:
                QMessageBox.warning(self, "警告", "请输入商品链接")
                return

            # 解析链接
            product_links = [link.strip() for link in links_text.split('\n') if link.strip()]
            if not product_links:
                QMessageBox.warning(self, "警告", "没有有效的商品链接")
                return

            # 获取查询参数
            date_range = self.query_date_combo.currentText()
            speed_mode = self.speed_combo.currentText()

            # 创建查询工作线程
            self.product_query_worker = ProductQueryWorker(product_links, date_range, speed_mode)

            # 连接信号
            self.product_query_worker.progress_updated.connect(self.on_query_progress)
            self.product_query_worker.error_occurred.connect(self.on_query_error)
            self.product_query_worker.result_ready.connect(self.on_query_result_ready)
            self.product_query_worker.query_finished.connect(self.on_query_finished)

            # 启动查询
            self.product_query_worker.start()

            # 更新界面状态
            self.start_query_btn.setEnabled(False)
            self.stop_query_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度

            # 清空表格
            self.query_table.setRowCount(0)
            self.query_results.clear()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动查询失败: {e}")

    def on_stop_query_clicked(self):
        """停止查询按钮点击事件"""
        try:
            if self.product_query_worker and self.product_query_worker.isRunning():
                self.product_query_worker.stop()
                self.status_label.setText("正在停止查询...")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"停止查询失败: {e}")

    def on_export_query_clicked(self):
        """导出查询结果按钮点击事件"""
        try:
            if not self.query_results:
                QMessageBox.information(self, "提示", "没有查询结果可导出")
                return

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存查询结果", "查询结果.csv", "CSV文件 (*.csv)"
            )

            if file_path:
                self.export_query_to_csv(self.query_results, file_path)
                QMessageBox.information(self, "成功", f"查询结果已导出到: {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出查询结果失败: {e}")

    # ========================================================================
    # 信号处理方法
    # ========================================================================
    def on_collection_progress(self, message):
        """采集进度更新"""
        self.status_label.setText(message)

    def on_collection_status(self, status):
        """采集状态变化"""
        self.status_label.setText(f"状态: {status}")

    def on_collection_error(self, error_message):
        """采集错误处理"""
        self.status_label.setText(f"错误: {error_message}")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                padding: 5px;
                margin: 2px;
            }
        """)
        QMessageBox.critical(self, "采集错误", error_message)

    def on_data_row_ready(self, data):
        """单行数据就绪"""
        try:
            # 添加到数据列表
            self.collected_data.append(data)

            # 添加到表格
            row = self.collection_table.rowCount()
            self.collection_table.insertRow(row)

            # 填充数据
            columns = [
                'itemId', 'itemTitle', 'currentGmv', 'currentOrderCnt', 'currentUv',
                'currentCvr', 'currentPrice', 'tradeIndex', 'channelRatio', 'rank',
                'categoryName', 'shopName', 'brandName'
            ]

            for col, key in enumerate(columns):
                value = data.get(key, '')
                if isinstance(value, (int, float)):
                    value = str(value)
                item = QTableWidgetItem(str(value))
                self.collection_table.setItem(row, col, item)

            # 滚动到最新行
            self.collection_table.scrollToBottom()

        except Exception as e:
            print(f"添加数据行失败: {e}")

    def on_collection_finished(self):
        """采集完成"""
        self.start_collection_btn.setEnabled(True)
        self.stop_collection_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.export_data_btn.setEnabled(len(self.collected_data) > 0)

        self.status_label.setText(f"采集完成，共获取 {len(self.collected_data)} 条数据")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
                border-radius: 4px;
                padding: 5px;
                margin: 2px;
            }
        """)

    def on_query_progress(self, message):
        """查询进度更新"""
        self.status_label.setText(message)

    def on_query_error(self, error_message):
        """查询错误处理"""
        self.status_label.setText(f"查询错误: {error_message}")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                padding: 5px;
                margin: 2px;
            }
        """)
        QMessageBox.critical(self, "查询错误", error_message)

    def on_query_result_ready(self, result):
        """查询结果就绪"""
        try:
            # 添加到结果列表
            self.query_results.append(result)

            # 添加到表格
            row = self.query_table.rowCount()
            self.query_table.insertRow(row)

            # 填充数据
            columns = ['product_id', 'product_title', 'product_link', 'total_sales', 'date_range']

            for col, key in enumerate(columns):
                value = result.get(key, '')
                item = QTableWidgetItem(str(value))
                self.query_table.setItem(row, col, item)

            # 滚动到最新行
            self.query_table.scrollToBottom()

        except Exception as e:
            print(f"添加查询结果失败: {e}")

    def on_query_finished(self):
        """查询完成"""
        self.start_query_btn.setEnabled(True)
        self.stop_query_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.export_query_btn.setEnabled(len(self.query_results) > 0)

        self.status_label.setText(f"查询完成，共获取 {len(self.query_results)} 条结果")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
                border-radius: 4px;
                padding: 5px;
                margin: 2px;
            }
        """)

    # ========================================================================
    # 辅助方法
    # ========================================================================
    def export_to_csv(self, data, file_path):
        """导出数据到CSV文件"""
        try:
            import csv

            if not data:
                return

            # 获取所有字段
            all_keys = set()
            for item in data:
                all_keys.update(item.keys())

            # 排序字段
            sorted_keys = sorted(all_keys)

            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=sorted_keys)
                writer.writeheader()
                for item in data:
                    writer.writerow(item)

        except Exception as e:
            raise Exception(f"导出CSV失败: {e}")

    def export_query_to_csv(self, results, file_path):
        """导出查询结果到CSV文件"""
        try:
            import csv

            if not results:
                return

            with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['product_id', 'product_title', 'product_link', 'total_sales', 'date_range']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                for result in results:
                    # 基础信息
                    row = {
                        'product_id': result.get('product_id', ''),
                        'product_title': result.get('product_title', ''),
                        'product_link': result.get('product_link', ''),
                        'total_sales': result.get('total_sales', 0),
                        'date_range': result.get('date_range', '')
                    }
                    writer.writerow(row)

        except Exception as e:
            raise Exception(f"导出查询结果失败: {e}")


# ============================================================================
# 主函数和程序入口
# ============================================================================
def main():
    """主函数"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)

        # 设置应用程序属性
        app.setApplicationName("快手采集工具")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("快手采集工具")

        # 设置应用程序样式
        app.setStyle('Fusion')

        # 设置调色板
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(240, 240, 240))
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
        palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 220))
        palette.setColor(QPalette.ToolTipText, QColor(0, 0, 0))
        palette.setColor(QPalette.Text, QColor(0, 0, 0))
        palette.setColor(QPalette.Button, QColor(240, 240, 240))
        palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
        palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        palette.setColor(QPalette.Link, QColor(42, 130, 218))
        palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        app.setPalette(palette)

        # 确保data目录存在
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)

        # 创建主窗口
        main_window = KuaishouCollectorMainWindow()
        main_window.show()

        # 运行应用程序
        sys.exit(app.exec_() if PYQT_VERSION == 5 else app.exec())

    except Exception as e:
        print(f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
